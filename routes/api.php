<?php

declare(strict_types=1);

use App\Http\Controllers\CityController;
use App\Http\Controllers\ContactUsController;
use App\Http\Controllers\DeliveryController;
use App\Http\Controllers\FAQController;
use App\Http\Controllers\OTPController;
use App\Http\Controllers\PlanController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\RegistrationRequestController;
use App\Http\Controllers\SMSController;
use App\Http\Middleware\JasminHttpCall;
use App\Http\Middleware\RestrictFrontendAccessMiddleware;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth:sanctum', 'project', 'ip-whitelist'])->group(function () {
    // SMS
    Route::group(['prefix' => 'sms'], function () {
        Route::post('messages', [SMSController::class, 'send']);
        Route::post('messages/bulk', [SMSController::class, 'bulk_send']);
        Route::post('messages/template', [SMSController::class, 'send_template']);
        Route::post('messages/contacts', [SMSController::class, 'contacts_send']);
        Route::get('messages/{message_id}', [SMSController::class, 'sms_inquiry']);
        Route::get('messages', [SMSController::class, 'index']);
    });

    // OTP
    Route::group(['prefix' => 'otp'], function () {
        Route::post('initiate', [OTPController::class, 'sendOTP'])->middleware(
            'otp-rate-limit',
        );
        Route::post('verify', [OTPController::class, 'verifyOTP']);
    });

    // Project
    Route::group(['prefix' => 'project'], function () {
        Route::get('/details', [ProjectController::class, 'details']);
        Route::get('/balance', [ProjectController::class, 'balance']);
        Route::get('/contacts/{group_id}', [
            ProjectController::class,
            'contacts',
        ]);
        Route::get('/consumptions', [ProjectController::class, 'consumptions']);
    });
});

Route::post('/delivery/{id}', [DeliveryController::class, 'index'])
    ->middleware(JasminHttpCall::class);

Route::middleware(RestrictFrontendAccessMiddleware::class)->group(function () {
    Route::get('/plans', [PlanController::class, 'index']);
    Route::get('/faq', [FAQController::class, 'index']);
    Route::get('/cities', [CityController::class, 'index']);
    Route::post('/registration-request', [RegistrationRequestController::class, 'register']);
    Route::post('/contact-us', [ContactUsController::class, 'store']);
});
