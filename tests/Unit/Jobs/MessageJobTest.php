<?php

declare(strict_types=1);

use App\Jobs\MessageJob;
use App\Models\Message;
use App\Models\MessageReceipt;
use App\Models\Provider;
use App\Models\Sender;
use App\Services\Jasmin\JasminClient;
use App\Services\Jasmin\Models\SentMessage;

use function Pest\Laravel\seed;

beforeEach(function () {
    seed();
    // Ensure required models exist
    $this->sender = Sender::factory()->create([
        'sender' => 'TestSender',
        'status' => 'active',
    ]);
    // Remove all providers and create the required one for the test
    Provider::query()->delete();
    Provider::factory()->create(['name' => 'Libyana']);
    $this->message = Message::factory()->create(['sender_id' => $this->sender->id, 'message_type' => 'sms']);
    $this->receipt = MessageReceipt::factory()->create([
        'message_id' => $this->message->id,
        'number' => '00218920000000',
        'status' => 'pending',
    ]);
    $this->message->setRelation('sender', $this->sender);
    $this->receipt->setRelation('message', $this->message);
});

it('marks message as sent on success', function () {
    $jasmin = Mockery::mock(JasminClient::class);
    $mockMsg = Mockery::mock(App\Services\Jasmin\Models\Message::class)->makePartial();
    $jasmin->shouldReceive('message')->andReturn($mockMsg);
    $mockMsg->shouldReceive('from')->andReturnSelf();
    $mockMsg->shouldReceive('to')->andReturnSelf();
    $mockMsg->shouldReceive('content')->andReturnSelf();
    $mockMsg->shouldReceive('coding')->andReturnSelf();
    $mockMsg->shouldReceive('via')->andReturnSelf();
    $mockMsg->shouldReceive('tags')->andReturnSelf();
    $mockMsg->shouldReceive('dlrCallbackUrl')->andReturnSelf();
    $mockMsg->shouldReceive('send')->andReturn(new SentMessage('Success', 'msgid-123'));

    $job = new MessageJob($this->receipt, $jasmin);
    $job->handle();
    $this->receipt->refresh();
    expect($this->receipt->status)->toBe('sent');
    expect($this->receipt->smpp_message_id)->toBe('msgid-123');
});

it('marks message as failed if not sent', function () {
    $jasmin = Mockery::mock(JasminClient::class);
    $mockMsg = Mockery::mock(App\Services\Jasmin\Models\Message::class)->makePartial();
    $jasmin->shouldReceive('message')->andReturn($mockMsg);
    $mockMsg->shouldReceive('from')->andReturnSelf();
    $mockMsg->shouldReceive('to')->andReturnSelf();
    $mockMsg->shouldReceive('content')->andReturnSelf();
    $mockMsg->shouldReceive('coding')->andReturnSelf();
    $mockMsg->shouldReceive('via')->andReturnSelf();
    $mockMsg->shouldReceive('tags')->andReturnSelf();
    $mockMsg->shouldReceive('dlrCallbackUrl')->andReturnSelf();
    $mockMsg->shouldReceive('send')->andReturn(new SentMessage('Failed', null));

    $job = new MessageJob($this->receipt, $jasmin);
    $job->handle();
    $this->receipt->refresh();
    expect($this->receipt->status)->toBe('failed');
});

it('fails if provider is unknown', function () {
    // Do not create provider for this test
    $receipt = MessageReceipt::factory()->create(['number' => '**********', 'message_id' => $this->message->id]);
    $receipt->setRelation('message', $this->message);
    $jasmin = Mockery::mock(JasminClient::class);
    $job = new MessageJob($receipt, $jasmin);
    expect(fn () => $job->handle())->toThrow(Illuminate\Database\Eloquent\ModelNotFoundException::class);
});

it('marks message as pending on exception and retries', function () {
    $jasmin = Mockery::mock(JasminClient::class);
    $mockMsg = Mockery::mock(App\Services\Jasmin\Models\Message::class)->makePartial();
    $jasmin->shouldReceive('message')->andReturn($mockMsg);
    $mockMsg->shouldReceive('from')->andReturnSelf();
    $mockMsg->shouldReceive('to')->andReturnSelf();
    $mockMsg->shouldReceive('content')->andReturnSelf();
    $mockMsg->shouldReceive('coding')->andReturnSelf();
    $mockMsg->shouldReceive('via')->andReturnSelf();
    $mockMsg->shouldReceive('tags')->andReturnSelf();
    $mockMsg->shouldReceive('dlrCallbackUrl')->andReturnSelf();
    $mockMsg->shouldReceive('send')->andThrow(new Exception('Jasmin error'));

    // Create a partial mock of MessageJob
    $job = Mockery::mock(MessageJob::class, [$this->receipt, $jasmin])->makePartial();
    $job->shouldReceive('attempts')->andReturn(1);
    $job->shouldReceive('release')->once()->with(10); // Should use the backoff value from the job

    $job->handle();
    $this->receipt->refresh();
    expect($this->receipt->status)->toBe('pending');
});

it('marks message as failed after max retries', function () {
    $jasmin = Mockery::mock(JasminClient::class);
    $mockMsg = Mockery::mock(App\Services\Jasmin\Models\Message::class)->makePartial();
    $jasmin->shouldReceive('message')->andReturn($mockMsg);
    $mockMsg->shouldReceive('from')->andReturnSelf();
    $mockMsg->shouldReceive('to')->andReturnSelf();
    $mockMsg->shouldReceive('content')->andReturnSelf();
    $mockMsg->shouldReceive('coding')->andReturnSelf();
    $mockMsg->shouldReceive('via')->andReturnSelf();
    $mockMsg->shouldReceive('tags')->andReturnSelf();
    $mockMsg->shouldReceive('dlrCallbackUrl')->andReturnSelf();
    $mockMsg->shouldReceive('send')->andThrow(new Exception('Jasmin error'));

    // Create a partial mock of MessageJob
    $job = Mockery::mock(MessageJob::class, [$this->receipt, $jasmin])->makePartial();
    $job->shouldReceive('attempts')->once()->andReturn(60); // Use the max tries value from the job
    $job->shouldReceive('fail')->once();

    $job->handle();
    $this->receipt->refresh();
    expect($this->receipt->status)->toBe('failed');
});

it('encodes UCS2 for non-GSM7 message', function () {
    $this->message->update(['short_message' => 'مرحبا']); // Arabic, not GSM7
    $this->receipt->refresh();
    $jasmin = Mockery::mock(JasminClient::class);
    $mockMsg = Mockery::mock(App\Services\Jasmin\Models\Message::class)->makePartial();
    $jasmin->shouldReceive('message')->andReturn($mockMsg);
    $mockMsg->shouldReceive('from')->andReturnSelf();
    $mockMsg->shouldReceive('to')->andReturnSelf();
    $mockMsg->shouldReceive('content')->andReturnSelf();
    $mockMsg->shouldReceive('coding')->andReturnSelf();
    $mockMsg->shouldReceive('via')->andReturnSelf();
    $mockMsg->shouldReceive('tags')->andReturnSelf();
    $mockMsg->shouldReceive('dlrCallbackUrl')->andReturnSelf();
    $mockMsg->shouldReceive('send')->andReturn(new SentMessage('Success', 'msgid-456'));

    $job = new MessageJob($this->receipt, $jasmin);
    $job->handle();
    $this->receipt->refresh();
    expect($this->receipt->status)->toBe('sent');
    expect($this->receipt->smpp_message_id)->toBe('msgid-456');
    expect($this->receipt->sent_at)->not->toBeNull();
});

it('handles flash message type', function () {
    $this->message->update(['message_type' => 'flash']);
    $this->receipt->refresh();
    $jasmin = Mockery::mock(JasminClient::class);
    $mockMsg = Mockery::mock(App\Services\Jasmin\Models\Message::class)->makePartial();
    $jasmin->shouldReceive('message')->andReturn($mockMsg);
    $mockMsg->shouldReceive('from')->andReturnSelf();
    $mockMsg->shouldReceive('to')->andReturnSelf();
    $mockMsg->shouldReceive('content')->andReturnSelf();
    $mockMsg->shouldReceive('coding')->andReturnSelf();
    $mockMsg->shouldReceive('via')->andReturnSelf();
    $mockMsg->shouldReceive('tags')->andReturnSelf();
    $mockMsg->shouldReceive('dlrCallbackUrl')->andReturnSelf();
    $mockMsg->shouldReceive('send')->andReturn(new SentMessage('Success', 'msgid-789'));

    $job = new MessageJob($this->receipt, $jasmin);
    $job->handle();
    $this->receipt->refresh();
    expect($this->receipt->status)->toBe('sent');
    expect($this->receipt->smpp_message_id)->toBe('msgid-789');
    expect($this->receipt->sent_at)->not->toBeNull();
});

it('getMessageReceipt and getJasmin return correct objects', function () {
    $jasmin = Mockery::mock(JasminClient::class);
    $job = new MessageJob($this->receipt, $jasmin);
    expect($job->getMessageReceipt())->toBeInstanceOf(MessageReceipt::class);
    expect($job->getJasmin())->toBe($jasmin);
});

it('failed method updates message receipt status', function () {
    $jasmin = Mockery::mock(JasminClient::class);
    $job = new MessageJob($this->receipt, $jasmin);
    $exception = new Exception('Test exception');

    $job->failed($exception);
    $this->receipt->refresh();

    expect($this->receipt->status)->toBe('failed');
});
