<?php

declare(strict_types=1);

use App\Models\RegistrationRequest;
use App\Models\User;

it('registers a new registration request successfully', function () {

    User::truncate();
    RegistrationRequest::truncate();
    // Arrange: Prepare valid request data
    $data = [
        'name' => '<PERSON>',
        'phone' => '1234567890',
        'email' => '<EMAIL>',
        'city' => 'New York',
        'company' => 'Example Inc.',
        'category' => 'IT',
        'have_smsapi' => 'yes',
        'target' => ['OTP', 'Campaign'],
        'system_type' => 'Custom',
        'notes' => 'Test note',
    ];

    // Act: Make a POST request to the endpoint
    $response = $this->postJson('/api/registration-request', $data);

    // Assert: Check the response
    $response->assertCreated()
        ->assertJson([
            'message' => 'The request has been registered successfully.',
        ]);

    // Assert: Check the database
    $this->assertDatabaseHas('registration_requests', [
        'email' => '<EMAIL>',
    ]);
});

it('returns conflict if email already exists', function () {
    // Arrange: Create a user and a registration request with the same email
    $email = '<EMAIL>';
    User::factory()->create(['email' => $email]);
    RegistrationRequest::factory()->create(['email' => $email, 'status' => 'pending']);

    $data = [
        'name' => 'Jane Doe',
        'phone' => '0987654321',
        'email' => $email,
    ];

    // Act: Make a POST request to the endpoint
    $response = $this->postJson('/api/registration-request', $data);

    // Assert: Check the response
    $response->assertStatus(409)
        ->assertJson([
            'message' => 'The email address is already exists.',
        ]);
});

it('returns validation errors for invalid data', function () {
    // Arrange: Prepare invalid request data
    $data = [
        'name' => '',
        'phone' => '',
        'email' => 'invalid-email',
    ];

    // Act: Make a POST request to the endpoint
    $response = $this->postJson('/api/registration-request', $data);

    // Assert: Check the response
    $response->assertStatus(422)
        ->assertJsonValidationErrors(['name', 'phone', 'email']);
});
