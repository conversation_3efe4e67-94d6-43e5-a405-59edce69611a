<?php

declare(strict_types=1);

use <PERSON><PERSON>hanSalleh\FilamentShield\Support\Utils;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $rolesWithPermissions = '[
              {
                "name": "super_admin",
                "guard_name": "web",
                "permissions": [
                    "view_campaign::message",
                    "view_any_campaign::message",
                    "create_campaign::message",
                    "update_campaign::message",
                    "restore_campaign::message",
                    "restore_any_campaign::message",
                  "replicate_campaign::message",
                  "reorder_campaign::message",
                  "delete_campaign::message",
                  "delete_any_campaign::message",
                  "force_delete_campaign::message",
                  "force_delete_any_campaign::message",
                  "view_city",
                  "view_any_city",
                  "create_city",
                  "update_city",
                  "restore_city",
                  "restore_any_city",
                  "replicate_city",
                  "reorder_city",
                  "delete_city",
                  "delete_any_city",
                  "force_delete_city",
                  "force_delete_any_city",
                  "view_company",
                  "view_any_company",
                  "create_company",
                  "update_company",
                  "restore_company",
                  "restore_any_company",
                  "replicate_company",
                  "reorder_company",
                  "delete_company",
                  "delete_any_company",
                  "force_delete_company",
                  "force_delete_any_company",
                  "view_log::activity",
                  "view_any_log::activity",
                  "create_log::activity",
                  "update_log::activity",
                  "restore_log::activity",
                  "restore_any_log::activity",
                  "replicate_log::activity",
                  "reorder_log::activity",
                  "delete_log::activity",
                  "delete_any_log::activity",
                  "force_delete_log::activity",
                  "force_delete_any_log::activity",
                  "view_plan",
                  "view_any_plan",
                  "create_plan",
                  "update_plan",
                  "restore_plan",
                  "restore_any_plan",
                  "replicate_plan",
                  "reorder_plan",
                  "delete_plan",
                  "delete_any_plan",
                  "force_delete_plan",
                  "force_delete_any_plan",
                  "view_provider",
                  "view_any_provider",
                  "create_provider",
                  "update_provider",
                  "restore_provider",
                  "restore_any_provider",
                  "replicate_provider",
                  "reorder_provider",
                  "delete_provider",
                  "delete_any_provider",
                  "force_delete_provider",
                  "force_delete_any_provider",
                  "view_role",
                  "view_any_role",
                  "create_role",
                  "update_role",
                  "delete_role",
                  "delete_any_role",
                  "view_sender",
                  "view_any_sender",
                  "create_sender",
                  "update_sender",
                  "restore_sender",
                  "restore_any_sender",
                  "replicate_sender",
                  "reorder_sender",
                  "delete_sender",
                  "delete_any_sender",
                  "force_delete_sender",
                  "force_delete_any_sender",
                  "view_subscription",
                  "view_any_subscription",
                  "create_subscription",
                  "update_subscription",
                  "restore_subscription",
                  "restore_any_subscription",
                  "replicate_subscription",
                  "reorder_subscription",
                  "delete_subscription",
                  "delete_any_subscription",
                  "force_delete_subscription",
                  "force_delete_any_subscription",
                  "view_transaction",
                  "view_any_transaction",
                  "create_transaction",
                  "update_transaction",
                  "restore_transaction",
                  "restore_any_transaction",
                  "replicate_transaction",
                  "reorder_transaction",
                  "delete_transaction",
                  "delete_any_transaction",
                  "force_delete_transaction",
                  "force_delete_any_transaction",
                  "view_user",
                  "view_any_user",
                  "create_user",
                  "update_user",
                  "restore_user",
                  "restore_any_user",
                  "replicate_user",
                  "reorder_user",
                  "delete_user",
                  "delete_any_user",
                  "force_delete_user",
                  "force_delete_any_user",
                  "view_any_message::template",
                  "view_message::template",
                  "create_message::template",
                  "update_message::template",
                  "delete_message::template",
                  "delete_any_message::template",
                    "view_message",
                    "view_any_message",
                    "create_message",
                    "update_message",
                    "restore_message",
                    "restore_any_message",
                    "view_any_faq",
                    "view_faq",
                    "create_faq",
                    "update_faq",
                    "delete_faq",
                    "delete_any_faq"
                ]
              },
              {
                "name": "company_owner",
                "guard_name": "web",
                "permissions": [
                  "view_role",
                  "view_any_role",
                  "create_role",
                  "update_role",
                  "delete_role",
                  "delete_any_role",
                  "view_user",
                  "view_any_user",
                  "create_user",
                  "update_user",
                  "restore_user",
                  "restore_any_user",
                  "replicate_user",
                  "reorder_user",
                  "delete_user",
                  "delete_any_user",
                  "force_delete_user",
                  "force_delete_any_user",
                  "view_campaign::message",
                  "view_any_campaign::message",
                  "create_campaign::message",
                  "update_campaign::message",
                  "restore_campaign::message",
                  "restore_any_campaign::message",
                  "replicate_campaign::message",
                  "reorder_campaign::message",
                  "delete_campaign::message",
                  "delete_any_campaign::message",
                  "force_delete_campaign::message",
                  "force_delete_any_campaign::message",
                  "view_contact::group",
                  "view_any_contact::group",
                  "create_contact::group",
                  "update_contact::group",
                  "restore_contact::group",
                  "restore_any_contact::group",
                  "replicate_contact::group",
                  "reorder_contact::group",
                  "delete_contact::group",
                  "delete_any_contact::group",
                  "force_delete_contact::group",
                  "force_delete_any_contact::group",
                  "view_message",
                  "view_any_message",
                  "create_message",
                  "update_message",
                  "restore_message",
                  "restore_any_message",
                  "replicate_message",
                  "reorder_message",
                  "delete_message",
                  "delete_any_message",
                  "force_delete_message",
                  "force_delete_any_message",
                  "view_project",
                  "view_any_project",
                  "create_project",
                  "update_project",
                  "restore_project",
                  "restore_any_project",
                  "replicate_project",
                  "reorder_project",
                  "delete_project",
                  "delete_any_project",
                  "force_delete_project",
                  "force_delete_any_project",
                  "view_role",
                  "view_any_role",
                  "create_role",
                  "update_role",
                  "delete_role",
                  "delete_any_role",
                  "view_sender",
                  "view_any_sender",
                  "create_sender",
                  "update_sender",
                  "restore_sender",
                  "restore_any_sender",
                  "replicate_sender",
                  "reorder_sender",
                  "delete_sender",
                  "delete_any_sender",
                  "force_delete_sender",
                  "force_delete_any_sender",
                  "view_subscription",
                  "view_any_subscription",
                  "create_subscription",
                  "update_subscription",
                  "restore_subscription",
                  "restore_any_subscription",
                  "replicate_subscription",
                  "reorder_subscription",
                  "delete_subscription",
                  "delete_any_subscription",
                  "force_delete_subscription",
                  "force_delete_any_subscription",
                  "view_transaction",
                  "view_any_transaction",
                  "create_transaction",
                  "update_transaction",
                  "restore_transaction",
                  "restore_any_transaction",
                  "replicate_transaction",
                  "reorder_transaction",
                  "delete_transaction",
                  "delete_any_transaction",
                  "force_delete_transaction",
                  "force_delete_any_transaction",
                  "view_user",
                  "view_any_user",
                  "create_user",
                  "update_user",
                  "restore_user",
                  "restore_any_user",
                  "replicate_user",
                  "reorder_user",
                  "delete_user",
                  "delete_any_user",
                  "force_delete_user",
                  "force_delete_any_user",
                  "view_any_message::template",
                  "view_message::template",
                  "create_message::template",
                  "update_message::template",
                  "delete_message::template",
                  "delete_any_message::template"
                ]
              }
            ]';
        $this->makeRolesWithPermissions($rolesWithPermissions);
    }

    private function makeRolesWithPermissions(string $rolesWithPermissions): void
    {
        if (! blank($rolePlusPermissions = json_decode($rolesWithPermissions, true))) {
            /** @var Model $roleModel */
            $roleModel = Utils::getRoleModel();
            /** @var Model $permissionModel */
            $permissionModel = Utils::getPermissionModel();

            foreach ($rolePlusPermissions as $rolePlusPermission) {
                $role = $roleModel::firstOrCreate([
                    'name' => $rolePlusPermission['name'],
                    'guard_name' => $rolePlusPermission['guard_name'],
                ]);

                if (! blank($rolePlusPermission['permissions'])) {
                    $permissionModels = collect($rolePlusPermission['permissions'])
                        ->map(fn ($permission) => $permissionModel::firstOrCreate([
                            'name' => $permission,
                            'guard_name' => $rolePlusPermission['guard_name'],
                        ]))
                        ->all();

                    /** @var Spatie\Permission\Models\Role $role */
                    $role->syncPermissions($permissionModels);
                }
            }
        }
    }
};
