<?php

declare(strict_types=1);

use App\Http\Middleware\OTPRateLimitMiddleware;
use App\Http\Middleware\ProjectMiddleware;
use App\Services\PhoneProvider;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withSingletons([
        //        PhoneProvider::class
    ])
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->alias([
            'otp-rate-limit' => OTPRateLimitMiddleware::class,
            'project' => ProjectMiddleware::class,
            'ip-whitelist' => App\Http\Middleware\IPWhitelistMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })
    ->create();
