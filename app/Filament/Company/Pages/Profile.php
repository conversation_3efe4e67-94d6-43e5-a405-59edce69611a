<?php

declare(strict_types=1);

namespace App\Filament\Company\Pages;

use App\Models\Company;
use Filament\Facades\Filament;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;

class Profile extends Page implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    public ?Company $company = null;

    protected static string $view = 'filament.company.pages.profile';

    protected static bool $shouldRegisterNavigation = false;

    public function getTitle(): string|Htmlable
    {
        return __('Company Profile');
    }

    public function mount(): void
    {
        $this->company = Filament::getTenant()->load('city');
        $this->form->fill($this->company->toArray());
    }

    public function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('download_required_documents')
                ->label('Download Required Documents')
                ->translateLabel()
                ->icon('heroicon-o-arrow-down-tray')
                ->color('danger')
                ->action(fn (): \Symfony\Component\HttpFoundation\BinaryFileResponse => $this->downloadRequiredDocuments()),
        ];
    }

    public function downloadRequiredDocuments(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $filePath = public_path('documents/required_documents.pdf');
        $fileName = 'required_documents.pdf';

        if (! file_exists($filePath)) {
            abort(404);
        }

        return response()->download($filePath, $fileName);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make()
                    ->columns(3)
                    ->schema([
                        Section::make(__('Information of the company'))
                            ->columnSpan(2)
                            ->columns(2)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Company Name')
                                    ->translateLabel()
                                    ->readOnly(),
                                TextInput::make('address')
                                    ->label('Address')
                                    ->translateLabel()
                                    ->readOnly(),
                                TextInput::make('phone')
                                    ->label('Phone')
                                    ->translateLabel()
                                    ->readOnly(),
                                TextInput::make('email')
                                    ->label('Email')
                                    ->translateLabel()
                                    ->email()
                                    ->readOnly(),
                                TextInput::make('category')
                                    ->label('Category')
                                    ->translateLabel()
                                    ->readOnly(),
                                TextInput::make('description')
                                    ->label('Description')
                                    ->translateLabel()
                                    ->readOnly(),
                                TextInput::make('city.name')
                                    ->label('City')
                                    ->translateLabel()
                                    ->readOnly(),
                            ]),
                        Grid::make()
                            ->columnSpan(1)
                            ->schema([
                                Section::make([
                                    Placeholder::make('status')
                                        ->label('Status')
                                        ->translateLabel()
                                        ->content(fn ($state) => __($state)),
                                ]),

                                Section::make(__('Company Documents'))->schema([
                                    SpatieMediaLibraryFileUpload::make('company_documents')
                                        ->label('')
                                        ->disk('local')
                                        ->visibility('private')
                                        ->downloadable()
                                        ->multiple()
                                        ->collection('company_documents'),
                                ]),

                                Section::make(__('Company Logo'))->schema([
                                    SpatieMediaLibraryFileUpload::make('logo')
                                        ->collection('company_logos')
                                        ->label('')
                                        ->disk('local')
                                        ->visibility('private')
                                        ->downloadable()
                                        ->responsiveImages(),
                                ]),
                            ]),
                    ]),
            ])
            ->statePath('data');
    }
}
