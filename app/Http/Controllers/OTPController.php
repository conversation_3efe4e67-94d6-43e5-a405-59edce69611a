<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\FeatureConsumptionType;
use App\Facades\PhoneProvider;
use App\Jobs\MessageJobCreator;
use App\Models\Company;
use App\Models\Message;
use App\Models\MessageReceipt;
use App\Models\OneTimePassword;
use App\Models\Project;
use App\Models\Sender;
use App\Models\Setting;
use App\Models\Transaction;
use App\Rules\PhoneNumberRule;
use App\Services\SenderHelper;
use DB;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Random\RandomException;
use Throwable;

final readonly class OTPController
{
    public function __construct(private SenderHelper $senderHelper) {}

    /**
     * @throws RandomException
     *
     * @api {post} /api/otp/initiate Initiate OTP
     *
     * @bodyParam lang required string [ar, en]
     * @bodyParam length required int [4, 6]
     * @bodyParam expiration required int in minutes [1, 5, 10]
     * @bodyParam sender required string
     * @bodyParam message_type nullable string [flash, sms] default: sms
     * @bodyParam payment_type required string [wallet, subscription]
     * @bodyParam receiver required phone number
     *
     * @response json {"request_id": "string", "cost": int}
     *
     * @group OTP
     */
    public function sendOTP(Request $request): JsonResponse
    {
        /** @var array{'lang':string, 'length':int, 'expiration':int, 'sender':string, 'message_type':string|null,'payment_type':string, 'receiver':string} $validatedData */
        $validatedData = $this->validateRequest($request);

        if (
            $this->checkPhoneNumberIfHaveActiveOTP($validatedData['receiver'])
        ) {
            return response()->json(
                [
                    'message' => 'You have already sent an OTP to this number',
                ],
                400,
            );
        }

        /** @var Project $project */
        $project = $request->user();

        // In your main logic
        try {
            if ($validatedData['payment_type'] === 'wallet') {
                $this->checkBalance($project);
            } elseif ($validatedData['payment_type'] === 'subscription') {
                $this->checkSubscription($project);
            } else {
                throw new Exception('Invalid payment type');
            }
        } catch (Exception $e) {
            return response()->json(
                [
                    'message' => $e->getMessage(),
                ],
                400,
            );
        }

        $sender = $this->getSender($validatedData['sender'], $project);

        if (! $sender instanceof Sender) {
            return response()->json(
                [
                    'message' => 'Sender not found',
                ],
                404,
            );
        }

        /** @var array<string> $providers */
        $providers = $sender->provider->pluck('provider.name')->toArray();

        if (
            ! $this->checkSenderProvider($providers, $validatedData['receiver'])
        ) {
            return response()->json(
                [
                    'message' => 'The receiver is not from the same provider',
                ],
                400,
            );
        }

        $message_type = 'sms';

        if (
            isset($validatedData['message_type']) &&
            $validatedData['message_type'] !== $message_type
        ) {
            $message_type = $validatedData['message_type'];
        }

        $message_body = $this->getMessageBody(
            $validatedData['lang'],
            $validatedData['length'],
            $validatedData['expiration'],
        );

        /** @var OneTimePassword $otp */
        $otp = $this->send(
            $sender,
            $validatedData,
            $project,
            $message_body,
            $message_type,
        );

        $single_sms_cost_str = Setting::where(
            'key',
            'single_sms_cost',
        )->first();

        $cost = __('one message');

        if ($single_sms_cost_str) {
            $cost = (int) $single_sms_cost_str->value / 1000;
        }

        return response()->json([
            'request_id' => $otp->id,
            'cost' => $cost,
        ]);
    }

    /**
     * @api {post} /api/otp/verify Verify OTP
     *
     * @bodyParam request_id required string
     * @bodyParam code required string
     *
     * @response json {"message": "string"}
     *
     * @group OTP
     */
    public function verifyOTP(Request $request): JsonResponse
    {
        $validatedData = Validator::make(
            [
                'request_id' => $request->request_id,
                'code' => $request->code,
            ],
            [
                'request_id' => 'required|uuid',
                'code' => 'required|string',
            ],
        )->validate();

        $otp = OneTimePassword::where(
            'id',
            $validatedData['request_id'],
        )->first();

        if (! $otp) {
            return response()->json(
                [
                    'message' => 'OTP not found',
                ],
                404,
            );
        }

        if ($otp->verified_at) {
            return response()->json(
                [
                    'message' => 'OTP already verified',
                ],
                400,
            );
        }

        if ($otp->isExpired()) {
            return response()->json(
                [
                    'message' => 'OTP has expired',
                ],
                401,
            );
        }

        if ($otp->code !== $validatedData['code']) {
            return response()->json(
                [
                    'message' => 'Invalid OTP',
                ],
                401,
            );
        }

        $otp->verified_at = now();
        $otp->save();

        return response()->json([
            'message' => 'OTP verified successfully',
        ]);
    }

    /**
     * @throws Exception
     */
    private function checkSubscription(Project $project): void
    {
        $balance = $project->balance('OTP');
        if ($balance < 1) {
            throw new Exception('Insufficient balance');
        }
    }

    /**
     * @throws Exception
     */
    private function checkBalance(Project $project): void
    {
        $company = $project->company;
        assert($company instanceof Company);
        $balance = $company->balance();
        $cost = Setting::where('key', 'single_sms_cost')->first();

        if (! ctype_digit((string) $cost?->value)) {
            logger()->error('Single SMS cost not found');
            // TODO: check for errors
            throw new Exception('Single SMS cost not found');
        }

        $limit = $project->limit;

        // get transactions for the last month to check if the project has exceeded the limit
        $transaction =
            $project
                ->transactions()
                ->where('created_at', '>=', now()->subMonth())
                ->sum('amount') / 1000;

        if ($transaction + $limit < $cost->value / 1000) {
            throw new Exception('Insufficient balance for the project');
        }

        if ($balance < $cost->value) {
            throw new Exception('Insufficient balance');
        }
    }

    /**
     * @param  array{'lang':string, 'length':int, 'expiration':int, 'sender':string, 'message_type':string|null,'payment_type':string, 'receiver':string}  $validatedData
     * @param  array{'message':string, 'code':string}  $message_body
     *
     * @throws Throwable
     */
    private function send(
        Sender $sender,
        array $validatedData,
        Project $project,
        array $message_body,
        string $message_type,
    ): OneTimePassword {
        return DB::transaction(function () use (
            $sender,
            $validatedData,
            $project,
            $message_body,
            $message_type,
        ) {
            $message = Message::create([
                'short_message' => $message_body['message'],
                'sender_id' => $sender->id,
                'company_id' => $project->company_id,
                'project_id' => $project->id,
                'message_type' => $message_type,
                'send_type' => 'single',
                'contact_group_id' => null,
                'message_consumption' => 1,
                'transaction_id' => null,
            ]);

            $provider = PhoneProvider::detectPhoneNumberProvider(
                $validatedData['receiver'],
            );

            $message->messages()->create([
                'number' => $validatedData['receiver'],
                'status' => 'pending',
            ]);

            $otp = OneTimePassword::create([
                'expiration_period' => $validatedData['expiration'],
                'length' => $validatedData['length'],
                'code' => $message_body['code'],
                'message_id' => $message->id,
            ]);

            if ($validatedData['payment_type'] === 'wallet') {
                /** @param array{'value': float} $cost */
                $cost = Setting::where('key', 'single_sms_cost')->firstOrFail();

                $transaction = Transaction::create([
                    'amount' => ((int) ($cost->value) / 1000) * -1,
                    'action_type' => 'charge',
                    'status' => 'completed',
                    'company_id' => $project->company_id,
                    'project_id' => $project->id,
                ]);

                $message->update([
                    'transaction_id' => $transaction->id,
                ]);
            } else {
                $project->consume(
                    FeatureConsumptionType::OTP->value,
                    1,
                    FeatureConsumptionType::OTP->value,
                );
            }

            // Get the JasminClient from the container and dispatch the job
            $jasminClient = app()->make(\App\Services\Jasmin\JasminClient::class);
            MessageJobCreator::dispatch($message, $jasminClient);

            return $otp;
        });
    }

    /** @param array<string> $providers */
    private function checkSenderProvider(
        array $providers,
        string $receiver,
    ): bool {
        return $this->senderHelper->checkSenderProvider($receiver, $providers);
    }

    private function getSender(string $sender, Project $project): ?Sender
    {
        return $this->senderHelper->getSender($sender, $project);
    }

    /**
     * @return array<mixed>
     */
    private function validateRequest(Request $request): array
    {
        return Validator::make(
            [
                'lang' => $request->lang,
                'length' => $request->length,
                'expiration' => $request->expiration,
                'sender' => $request->sender,
                'message_type' => $request->message_type,
                'payment_type' => $request->payment_type,
                'receiver' => $request->receiver,
            ],
            [
                'lang' => 'required|string|in:ar,en',
                'length' => 'required|integer|in:4,6',
                'expiration' => 'required|integer|in:1,5,10',
                'sender' => 'required|string',
                'message_type' => 'nullable|string|in:flash,sms',
                'payment_type' => 'required|string|in:wallet,subscription',
                'receiver' => ['required', new PhoneNumberRule()],
            ],
        )->validate();
    }

    /**
     * @return array{'message':string, 'code':string}
     *
     * @throws RandomException
     */
    private function getMessageBody(
        string $lang,
        int $length,
        int $expiration,
    ): array {
        $code = mb_str_pad(
            (string) random_int(0, 10 ** $length - 1),
            $length,
            '0',
            STR_PAD_LEFT,
        );

        if ($lang === 'ar') {

            $time = $expiration === 1 ? '1 دقيقة' : $expiration.' دقائق';
            $message =
                $code.
                ' '.
                "رمز التحقق، وهو صالح لمدة $time. لا تشاركه مع أي أحد.";
        } else {
            $time = $expiration === 1 ? '1 minute' : $expiration.' minutes';
            $message =
                $code.' '."OTP, valid for $time. Do not share with anyone.";
        }

        return [
            'message' => $message,
            'code' => $code,
        ];
    }

    private function checkPhoneNumberIfHaveActiveOTP(string $phoneNumber): bool
    {
        $lastMessage = MessageReceipt::where('number', $phoneNumber)
            ->latest('id')
            ->first();

        if (! $lastMessage) {
            return false;
        }

        $otp = OneTimePassword::where('message_id', $lastMessage->message_id)
            ->latest('id')
            ->first();

        return $otp && ! $otp->isExpired();
    }
}
