<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\FeatureConsumptionType;
use App\Facades\PhoneProvider;
use App\Jobs\MessageJobCreator;
use App\Models\Company;
use App\Models\Contact;
use App\Models\ContactGroup;
use App\Models\Message;
use App\Models\MessageReceipt;
use App\Models\MessageTemplate;
use App\Models\Project;
use App\Models\Sender;
use App\Models\Setting;
use App\Models\Transaction;
use App\Rules\PhoneNumberRule;
use App\Services\SenderHelper;
use App\Services\SmsService;
use Carbon\Carbon;
use Closure;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Throwable;

final readonly class SMSController
{
    public function __construct(private SenderHelper $senderHelper) {}

    /**
     * @api {get} /api/sms/messages Get all messages
     *
     * @bodyParam page int nullable
     * @bodyParam per_page int nullable
     * @bodyParam from date nullable
     * @bodyParam to date nullable
     *
     * @response json
     *
     * @group SMS
     */
    public function index(Request $request): JsonResponse
    {
        /** @var array{'page':int|null, 'per_page':int|null,'from':Carbon|null,'to':Carbon|null} $validatedData */
        $validatedData = Validator::make(
            [
                'page' => $request->page,
                'per_page' => $request->per_page,
                'from' => $request->from,
                'to' => $request->to,
            ],
            [
                'page' => 'nullable|integer|min:1',
                'per_page' => 'nullable|integer|min:1',
                'from' => 'nullable|date',
                'to' => 'nullable|date',
            ],
        )->validate();

        /** @var Project $project */
        $project = $request->user();

        $messageQuery = $project->messages()->select([
            'id',
            'short_message',
            'message_type',
            'send_type',
            'contact_group_id',
            'message_consumption',
            'messages.id',
            'created_at',
            'updated_at',
        ])->with('messages:message_id,number,sent_at,delivered_at,status,delivery_report,created_at,updated_at');

        if ($validatedData['from']) {
            $messageQuery->where('created_at', '>=', $validatedData['from']);
        }

        if ($validatedData['to']) {
            $messageQuery->where('created_at', '<=', $validatedData['to']);
        }

        $messageQuery->orderBy('created_at', 'desc');

        $messages = $messageQuery->paginate($validatedData['per_page'] ?? 10);

        $messages->getCollection()->transform(fn (Message $message) => $message->makeHidden([
            'id',
            'messages',
        ]));

        foreach ($messages as $message) {
            $message->setAttribute(
                'receiver',
                $message->messages->makeHidden([
                    'id',
                    'message_id',
                    'smpp_message_id',
                    'contact_id',
                ]),
            );
        }

        return response()->json([
            'data' => $messages,
        ]);
    }

    /**
     * @throws Throwable
     *
     * @api {post} /api/sms/messages Send a message
     *
     * @bodyParam message required string
     * @bodyParam sender required string
     * @bodyParam message_type nullable string [flash, sms] default: sms
     * @bodyParam payment_type required string [wallet, subscription]
     * @bodyParam receiver required phone number
     *
     * @response json
     *
     * @group SMS
     */
    public function send(Request $request): JsonResponse
    {
        /** @var array{'message':string,'sender':string,'message_type':string,'payment_type':string,'receiver':string} $validatedData */
        $validatedData = Validator::make(
            [
                'message' => $request->message,
                'sender' => $request->sender,
                'message_type' => $request->message_type,
                'payment_type' => $request->payment_type,
                'receiver' => $request->receiver,
            ],
            [
                'message' => 'required|string',
                'sender' => 'required|string',
                'message_type' => 'nullable|string|in:flash,sms',
                'payment_type' => 'required|string|in:wallet,subscription',
                'receiver' => ['required', new PhoneNumberRule()],
            ],
        )->validate();

        /** @var Project $project */
        $project = $request->user();
        /** @var Company $company */
        $company = $project->company;

        $parts = $this->getSMSParts($validatedData['message']);

        try {
            $this->checkBalance(
                $parts,
                $validatedData['receiver'],
                $company,
                $validatedData['payment_type'],
                $project,
            );
        } catch (Exception $e) {
            return response()->json(
                [
                    'message' => $e->getMessage(),
                ],
                400,
            );
        }

        $sender = $this->senderHelper->getSender($validatedData['sender'], $project);

        if (! $sender instanceof Sender) {
            return response()->json(
                [
                    'message' => 'Sender not found',
                ],
                404,
            );
        }

        $message_type = $validatedData['message_type'] ?: 'sms';

        /** @var array<string> $providers */
        $providers = $sender->provider
            ->pluck('provider.name')
            ->map(fn (mixed $provider): mixed => is_string($provider) ? mb_strtolower($provider) : $provider)
            ->toArray();

        if (
            ! $this->senderHelper->checkSenderProvider(
                $validatedData['receiver'],
                $providers,
            )
        ) {
            return response()->json(
                [
                    'message' => 'The receiver is not from the same provider',
                ],
                400,
            );
        }

        $message_id = \DB::transaction(function () use (
            $sender,
            $validatedData,
            $project,
            $parts,
            $message_type,
        ) {
            $message = Message::create([
                'short_message' => $validatedData['message'],
                'sender_id' => $sender->id,
                'company_id' => $project->company_id,
                'project_id' => $project->id,
                'message_type' => $message_type,
                'send_type' => 'single',
                'contact_group_id' => null,
                'message_consumption' => $parts,
                'transaction_id' => null,
            ]);

            $message->messages()->create([
                'number' => $validatedData['receiver'],
                'status' => 'pending',
            ]);

            $this->handleBalanceConsumption(
                $parts,
                $validatedData['receiver'],
                $message,
                $validatedData['payment_type'],
                $project,
            );

            // Get the JasminClient from the container and dispatch the job
            if ($project->company && $project->company->auto_approve) {
                $message->update(['status' => 'approved']);

                $jasminClient = app()->make(\App\Services\Jasmin\JasminClient::class);
                MessageJobCreator::dispatch($message, $jasminClient);
            }

            return $message->id;
        });

        return response()->json([
            'message_id' => $message_id,
            'cost' => $parts,
        ]);
    }

    /**
     * @api {post} /api/sms/messages/bulk Send a bulk message
     *
     * @bodyParam message required string
     * @bodyParam sender required string
     * @bodyParam message_type nullable string [flash, sms] default: sms
     * @bodyParam payment_type required string [wallet, subscription]
     * @bodyParam receivers array of phone numbers required
     *
     * @response json
     *
     * @group SMS
     */
    public function bulk_send(Request $request): JsonResponse
    {
        /** @var array{'message':string,'sender':string,'message_type':string,'payment_type':string,'receivers':string[]} $validatedData */
        $validatedData = Validator::make(
            [
                'message' => $request->message,
                'sender' => $request->sender,
                'message_type' => $request->message_type,
                'payment_type' => $request->payment_type,
                'receivers' => $request->receivers,
            ],
            [
                'message' => 'required|string',
                'sender' => 'required|string',
                'message_type' => 'nullable|string|in:flash,sms',
                'payment_type' => 'required|string|in:wallet,subscription',
                'receivers' => 'required|array',
                'receivers.*' => ['required', new PhoneNumberRule()],
            ],
        )->validate();

        /** @var Project $project */
        $project = $request->user();
        /** @var Company $company */
        $company = $project->company;

        $parts = $this->getSMSParts($validatedData['message']);

        /** @var Collection<int, string> $receivers */
        $receivers = collect(
            $this->removeDuplicateRecipients($validatedData['receivers']),
        );

        try {
            $this->checkBalance(
                $parts,
                $receivers,
                $company,
                $validatedData['payment_type'],
                $project,
            );
        } catch (Exception $e) {
            return response()->json(
                [
                    'message' => $e->getMessage(),
                ],
                400,
            );
        }

        $message_type = $validatedData['message_type'] ?: 'sms';

        $sender = $this->senderHelper->getSender($validatedData['sender'], $project);

        if (! $sender instanceof Sender) {
            return response()->json(
                [
                    'message' => 'Sender not found',
                ],
                404,
            );
        }

        /** @var array<string> $providers */
        $providers = $sender->provider
            ->pluck('provider.name')
            ->map(fn (mixed $provider): mixed => is_string($provider) ? mb_strtolower($provider) : $provider)
            ->toArray();

        try {
            $compatibleReceivers = $receivers->filter(fn (string $receiver): bool => $this->senderHelper->checkSenderProvider($receiver, $providers));

            if ($compatibleReceivers->isEmpty()) {
                throw new Exception(
                    'The sender and receivers are not compatible.',
                );
            }
        } catch (Exception $e) {
            return response()->json(
                [
                    'message' => $e->getMessage(),
                ],
                400,
            );
        }

        $message_id = \DB::transaction(function () use (
            $sender,
            $validatedData,
            $project,
            $parts,
            $compatibleReceivers,
            $message_type,
        ) {
            // Step 1: Create the main Message entry
            $message = $this->createMessage(
                $validatedData['message'],
                $sender,
                $project,
                $message_type,
                $parts,
                $compatibleReceivers,
            );

            $this->processReceivers($compatibleReceivers, $message, $project);

            $this->handleBalanceConsumption(
                $parts,
                $compatibleReceivers,
                $message,
                $validatedData['payment_type'],
                $project,
            );

            //            QueuingMessage::dispatch($message->toJson())->onQueue('sms');

            return $message->id;
        });

        return response()->json([
            'message_id' => $message_id,
            'cost' => $parts * $receivers->count(),
            'details' => [
                'sent' => $compatibleReceivers->count(),
                'total' => count($validatedData['receivers']),
            ],
        ]);
    }

    /**
     * @api {post} /api/sms/messages/contacts Send a bulk message
     *
     * @bodyParam message required string
     * @bodyParam sender required string
     * @bodyParam message_type nullable string [flash, sms] default: sms
     * @bodyParam payment_type required string [wallet, subscription]
     * @bodyParam contact_group_id required string uuid
     *
     * @response json
     *
     * @group SMS
     */
    public function contacts_send(Request $request): JsonResponse
    {
        /** @var array{'message':string,'sender':string,'message_type':string,'payment_type':string,'contact_group_id':string} $validatedData */
        $validatedData = Validator::make(
            [
                'message' => $request->message,
                'sender' => $request->sender,
                'message_type' => $request->message_type,
                'payment_type' => $request->payment_type,
                'contact_group_id' => $request->contact_group_id,
            ],
            [
                'message' => 'required|string',
                'sender' => 'required|string',
                'message_type' => 'nullable|string|in:flash,sms',
                'payment_type' => 'required|string|in:wallet,subscription',
                'contact_group_id' => 'required|string|uuid',
            ],
        )->validate();

        /** @var Project $project */
        $project = $request->user();
        /** @var Company $company */
        $company = $project->company;

        try {
            $contactGroup = $project
                ->contact_groups()
                ->where('contact_group_id', $validatedData['contact_group_id'])
                ->get();

            $contacts = ContactGroup::find($validatedData['contact_group_id'])
                ?->contacts;

            // send to all contact
            if ($contactGroup->isEmpty() || ! $contacts) {
                throw new Exception('Contact group not found');
            }
        } catch (Exception $e) {
            return response()->json(
                [
                    'message' => $e->getMessage(),
                ],
                400,
            );
        }

        $parts = $this->getSMSParts($validatedData['message']);

        try {
            $this->checkBalance(
                $parts,
                $contacts,
                $company,
                $validatedData['payment_type'],
                $project,
            );
        } catch (Exception $e) {
            return response()->json(
                [
                    'message' => $e->getMessage(),
                ],
                400,
            );
        }

        $message_type = 'sms';

        if ($validatedData['message_type']) {
            $message_type = $validatedData['message_type'];
        }

        $sender = $this->senderHelper->getSender($validatedData['sender'], $project);

        if (! $sender instanceof Sender) {
            return response()->json(
                [
                    'message' => 'Sender not found',
                ],
                404,
            );
        }

        /** @var array<string> $providers */
        $providers = $sender->provider
            ->pluck('provider.name')
            ->map(fn (mixed $provider): mixed => is_string($provider) ? mb_strtolower($provider) : $provider)
            ->toArray();

        try {
            $compatibleReceivers = $contacts->filter(fn (Contact $receiver): bool => $this->senderHelper->checkSenderProvider(
                $receiver->phone,
                $providers,
            ));

            if ($compatibleReceivers->isEmpty()) {
                throw new Exception(
                    'The sender and receivers are not compatible.',
                );
            }
        } catch (Exception $e) {
            return response()->json(
                [
                    'message' => $e->getMessage(),
                ],
                400,
            );
        }

        $message_id = DB::transaction(function () use (
            $sender,
            $validatedData,
            $project,
            $parts,
            $compatibleReceivers,
            $message_type,
        ) {
            $message = $this->createMessage(
                $validatedData['message'],
                $sender,
                $project,
                $message_type,
                $parts,
                $compatibleReceivers,
            );
            $this->processReceivers($compatibleReceivers, $message, $project);
            $this->handleBalanceConsumption(
                $parts,
                $compatibleReceivers,
                $message,
                $validatedData['payment_type'],
                $project,
            );

            return $message->id;
        });

        return response()->json([
            'message_id' => $message_id,
            'cost' => $parts * $contacts->count(),
            'details' => [
                'sent' => $compatibleReceivers->count(),
                'total' => $contacts->count(),
            ],
        ]);
    }

    /**
     * @api {get} /api/sms/messages/message_id Get a message by id
     *
     * @queryParam message_id uuid required
     *
     * @response json
     *
     * @group SMS
     */
    public function sms_inquiry(Request $request): JsonResponse
    {
        $validatedData = Validator::make(
            [
                'message_id' => $request->message_id,
            ],
            [
                'message_id' => 'required|uuid',
            ],
        )->validate();

        /** @var Project $project */
        $project = $request->user();

        $message = Message::where('id', $validatedData['message_id'])
            ->where('company_id', $project->company_id)
            ->first();

        if (! $message) {
            return response()->json(
                [
                    'message' => 'Message not found',
                ],
                404,
            );
        }

        $message->makeHidden(
            'id',
            'sender_id',
            'company_id',
            'project_id',
            'transaction_id',
            'created_at',
            'updated_at',
        );
        $message->setAttribute(
            'receiver',
            $message->messages->each(function (MessageReceipt $receiver): void {
                $receiver->makeHidden(
                    'id',
                    'message_id',
                    'smpp_message_id',
                    'contact_id',
                );
            }),
        );
        $message->makeHidden('messages');

        return response()->json([$message]);
    }

    /**
     * @throws Throwable
     *
     * @api {post} /api/sms/messages/template Send a message using template
     *
     * @bodyParam template_id required string
     * @bodyParam sender required string
     * @bodyParam message_type nullable string [flash, sms] default: sms
     * @bodyParam payment_type required string [wallet, subscription]
     * @bodyParam receiver required phone number
     * @bodyParam params array required
     *
     * @response json
     *
     * @group SMS
     */
    public function send_template(Request $request): JsonResponse
    {
        /**
         * @var array{
         *     template_name: string,
         *     sender: string,
         *     message_type?: string,
         *     payment_type: string,
         *     receiver: string,
         *     params: array<int, array<string, string>>
         * } $validatedData
         */
        $validatedData = Validator::make(
            [
                'template_name' => $request->template_id,
                'sender' => $request->sender,
                'message_type' => $request->message_type,
                'payment_type' => $request->payment_type,
                'receiver' => $request->receiver,
                'params' => $request->params,
            ],
            [
                'template_name' => 'required|string',
                'sender' => 'required|string',
                'message_type' => 'nullable|string|in:flash,sms',
                'payment_type' => 'required|string|in:wallet,subscription',
                'receiver' => ['required', new PhoneNumberRule()],
                'params' => 'required|array',
                'params.*' => 'required|array',
                'params.*.*' => 'required|string',
            ]
        )->validate();

        /** @var Project $project */
        $project = $request->user();

        /** @var Company $company */
        $company = $project->company;

        $sender = $this->senderHelper->getSender($validatedData['sender'], $project);

        if (! $sender instanceof Sender) {
            return response()->json(
                [
                    'message' => 'Sender not found',
                ],
                404,
            );
        }

        /** @var array<string> $providers */
        $providers = $sender->provider
            ->pluck('provider.name')
            ->map(fn (mixed $provider): mixed => is_string($provider) ? mb_strtolower($provider) : $provider)
            ->toArray();

        if (
            ! $this->senderHelper->checkSenderProvider(
                $validatedData['receiver'],
                $providers,
            )
        ) {
            return response()->json(
                [
                    'message' => 'The receiver is not from the same provider',
                ],
                400,
            );
        }

        $template = MessageTemplate::where('short_name', $validatedData['template_name'])
            ->where('company_id', $project->company_id)
            ->where('project_id', $project->id)
            ->where('status', 'active')
            ->first();

        if (! $template) {
            return response()->json(
                [
                    'message' => 'Template not found',
                ],
                404,
            );
        }

        // check if the template has all the parameters with the right size
        foreach ($template->parameters as $parameter) {
            $paramValue = collect($validatedData['params'])
                ->first(fn (array $param): bool => array_key_exists($parameter->name, $param))[$parameter->name] ?? null;

            if (is_null($paramValue)) {
                return response()->json([
                    'message' => 'Missing parameter: '.$parameter->name,
                ], 400);
            }

            if ($parameter->max_limit && mb_strlen($paramValue) > $parameter->max_limit) {
                return response()->json([
                    'message' => 'Parameter "'.$parameter->name.'" exceeds max limit of '.$parameter->max_limit,
                ], 400);
            }
        }

        $message = $template->content;

        foreach ($validatedData['params'] as $param) {
            foreach ($param as $key => $value) {
                $message = str_replace('{{'.$key.'}}', $value, $message);
            }
        }

        $parts = $this->getSMSParts($message);

        try {
            $this->checkBalance(
                $parts,
                $validatedData['receiver'],
                $company,
                $validatedData['payment_type'],
                $project,
            );
        } catch (Exception $e) {
            return response()->json(
                [
                    'message' => $e->getMessage(),
                ],
                400,
            );
        }

        $message_id = \DB::transaction(function () use (
            $sender,
            $validatedData,
            $project,
            $parts,
            $message,
        ) {
            $message = Message::create([
                'short_message' => $message,
                'sender_id' => $sender->id,
                'company_id' => $project->company_id,
                'project_id' => $project->id,
                'message_type' => 'sms',
                'send_type' => 'single',
                'contact_group_id' => null,
                'message_consumption' => $parts,
                'transaction_id' => null,
                'status' => 'approved',
            ]);

            $message->messages()->create([
                'number' => $validatedData['receiver'],
                'status' => 'pending',
            ]);

            $this->handleBalanceConsumption(
                $parts,
                $validatedData['receiver'],
                $message,
                $validatedData['payment_type'],
                $project,
            );

            // Get the JasminClient from the container and dispatch the job
            $jasminClient = app()->make(\App\Services\Jasmin\JasminClient::class);
            MessageJobCreator::dispatch($message, $jasminClient);

            return $message->id;
        });

        return response()->json([
            'message_id' => $message_id,
            'cost' => $parts,
        ]);
    }

    /**
     * @param  Collection<int,Contact>|Collection<int,string>|string  $receivers
     *
     * @throws Exception
     */
    public function checkBalance(
        int $parts,
        Collection|string $receivers,
        Company $company,
        string $type,
        Project $project,
    ): void {
        if ($type === 'wallet') {
            $cost = Setting::where('key', 'single_sms_cost')->first();
            $balance = $company->balance();
            $limit = $project->limit;
            $transaction =
                $project
                    ->transactions()
                    ->where('created_at', '>=', now()->subMonth())
                    ->sum('amount') / 1000;

            $total =
                $receivers instanceof Collection
                    ? ($receivers->count() * $parts * (float) $cost?->value) /
                        1000
                    : ($parts * (float) $cost?->value) / 1000;

            if ($total > $limit) {
                throw new Exception('project limit exceeded');
            }

            if ($transaction + $limit < $total) {
                throw new Exception('Insufficient balance for the project');
            }

            if ($balance < $total) {
                throw new Exception('Insufficient balance');
            }
        } elseif ($type === 'subscription') {
            $balance = $project->balance('SMS');

            $total =
                $receivers instanceof Collection
                    ? $parts * $receivers->count()
                    : $parts;

            if ($balance < $total) {
                throw new Exception('Insufficient balance');
            }
        }
    }

    /**
     * @return Closure(Contact|string): array<string, mixed>
     */
    public function receiversMapper(): Closure
    {
        return function (Contact|string $receiver): array {
            if (is_string($receiver)) {
                return [
                    'number' => $receiver,
                    'status' => 'pending',
                ];
            }

            return [
                'number' => $receiver->phone,
                'status' => 'pending',
                'contact_id' => $receiver->id,
            ];
        };
    }

    /**
     * @param  Collection<int,Contact>|Collection<int,string>|string  $receivers
     */
    private function createMessage(
        string $message,
        Sender $sender,
        Project $project,
        string $message_type,
        int $parts,
        Collection|string $receivers,
    ): Message {
        return Message::create([
            'short_message' => $message,
            'sender_id' => $sender->id,
            'company_id' => $project->company_id,
            'project_id' => $project->id,
            'message_type' => $message_type,
            'send_type' => 'multiple',
            'contact_group_id' => null,
            'message_consumption' => $receivers instanceof Collection
                ? $parts * $receivers->count()
                : $parts,
            'transaction_id' => null,
        ]);
    }

    /**
     * @param  Collection<int,Contact>|Collection<int,string>  $receivers
     */
    private function processReceivers(
        Collection $receivers,
        Message $message,
        Project $project
    ): void {
        // Step 2: Group receivers by provider
        /** @var Collection<string, Collection<int, string>> $groupedReceivers */
        $groupedReceivers = $receivers->groupBy(
            fn (
                string|Contact $receiver,
            ): string => PhoneProvider::detectPhoneNumberProvider(
                is_string($receiver) ? $receiver : $receiver->phone,
            ),
        );

        // Step 3: Iterate through providers
        foreach ($groupedReceivers as $providerReceivers) {
            // Step 4: Batch receivers (max 254 per batch)
            $providerReceivers
                ->chunk(254)
                ->each(function (Collection $receiverBatch) use (
                    $message,
                    $project
                ): void {
                    // Step 5: Create a new SmppTaskQueue entry

                    // Step 6: Insert related messages for this batch
                    $batch = $receiverBatch
                        ->map(
                            $this->receiversMapper());
                    $message->messages()->createMany(
                        $batch,
                    );

                    if ($project->company && $project->company->auto_approve) {
                        $message->update(['status' => 'approved']);
                        // Get the JasminClient from the container and dispatch the job
                        $jasminClient = app()->make(\App\Services\Jasmin\JasminClient::class);
                        MessageJobCreator::dispatch($message, $jasminClient);
                    }
                });
        }
    }

    /**
     * @param  Collection<int, Contact>|Collection<int, string>|string  $receivers
     */
    private function handleBalanceConsumption(
        int $parts,
        Collection|string $receivers,
        Message $message,
        string $payment_type,
        Project $project,
    ): void {
        if ($payment_type === 'wallet') {
            /** @var Setting|null $cost */
            $cost = Setting::where('key', 'single_sms_cost')->first();
            $total =
                $receivers instanceof Collection
                    ? (count($receivers) * $parts * (float) $cost?->value) /
                        1000
                    : ($parts * (float) $cost?->value) / 1000;
            $transaction = Transaction::create([
                'amount' => $total * -1,
                'action_type' => 'charge',
                'status' => 'completed',
                'company_id' => $project->company_id,
                'project_id' => $project->id,
            ]);
            $message->update([
                'transaction_id' => $transaction->id,
            ]);
        } elseif (! $receivers instanceof Collection) {
            $project->consume(
                FeatureConsumptionType::SMS->value,
                $parts,
                FeatureConsumptionType::SMS->value,
            );
        } else {
            $project->consume(
                FeatureConsumptionType::SMS->value,
                $parts * count($receivers),
                FeatureConsumptionType::SMS->value,
            );
        }
    }

    private function getSMSParts(string $message): int
    {
        $result = SmsService::calculateSmsParts($message);

        return $result['parts'];
    }

    /**
     * @param  string[]|Collection<string, string>  $receivers
     * @return string[]
     */
    private function removeDuplicateRecipients(
        array|Collection $receivers,
    ): array {
        $result = [];
        foreach ($receivers as $receiver) {
            if (! in_array($receiver, $result)) {
                $result[] = $receiver;
            }
        }

        return $result;
    }
}
